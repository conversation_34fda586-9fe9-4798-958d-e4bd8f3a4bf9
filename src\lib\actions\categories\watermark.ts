// 水印类动作定�?

import { CategoryIds, ActionIds } from "../../constants";
import type { Action } from "../../types/action";
import { applyActionMapping } from "../action-mappings";

// 水印类动作定�?
const rawWatermarkActions = [
  {
    id: ActionIds.TEXT_WATERMARK,
    nameKey: "actions.text_watermark.name",
    descriptionKey: "actions.text_watermark.description",
    categoryId: CategoryIds.WATERMARK,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 10,
    params: [
      {
        key: "text",
        type: "string",
        nameKey: "actions.text_watermark.params.text",
        required: true,
        defaultValue: "水印文字",
      },
      {
        key: "position",
        type: "select",
        nameKey: "actions.text_watermark.params.position",
        required: true,
        defaultValue: "左上",
        options: [
          { value: "左上", labelKey: "positions.top_left" },
          { value: "右上", labelKey: "positions.top_right" },
          { value: "左下", labelKey: "positions.bottom_left" },
          { value: "右下", labelKey: "positions.bottom_right" },
          { value: "自定义", labelKey: "positions.custom" },
        ],
      },
      {
        key: "fontSize",
        type: "range",
        nameKey: "actions.text_watermark.params.fontSize",
        required: true,
        defaultValue: 24,
        min: 8,
        max: 72,
        step: 1,
      },
      {
        key: "fontColor",
        type: "color",
        nameKey: "actions.text_watermark.params.fontColor",
        required: true,
        defaultValue: "#ffffff",
      },
      {
        key: "opacity",
        type: "range",
        nameKey: "actions.text_watermark.params.opacity",
        required: true,
        defaultValue: 0.5,
        min: 0.1,
        max: 1.0,
        step: 0.1,
      },
      {
        key: "customX",
        type: "number",
        nameKey: "actions.text_watermark.params.customX",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
      {
        key: "customY",
        type: "number",
        nameKey: "actions.text_watermark.params.customY",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.text || params.text.trim() === "") {
        errors.push("actions.text_watermark.errors.empty_text");
      }
      if (params.fontSize < 8 || params.fontSize > 72) {
        errors.push("actions.text_watermark.errors.invalid_font_size");
      }
      if (params.opacity < 0.1 || params.opacity > 1.0) {
        errors.push("actions.text_watermark.errors.invalid_opacity");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.IMAGE_WATERMARK,
    nameKey: "actions.image_watermark.name",
    descriptionKey: "actions.image_watermark.description",
    categoryId: CategoryIds.WATERMARK,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 20,
    params: [
      {
        key: "watermarkPath",
        type: "image-file",
        nameKey: "actions.image_watermark.params.watermarkPath",
        required: true,
        defaultValue: "",
      },
      {
        key: "position",
        type: "select",
        nameKey: "actions.image_watermark.params.position",
        required: true,
        defaultValue: "左上",
        options: [
          { value: "左上", labelKey: "positions.top_left" },
          { value: "右上", labelKey: "positions.top_right" },
          { value: "左下", labelKey: "positions.bottom_left" },
          { value: "右下", labelKey: "positions.bottom_right" },
          { value: "自定义", labelKey: "positions.custom" },
        ],
      },
      {
        key: "width",
        type: "number",
        nameKey: "actions.image_watermark.params.width",
        required: true,
        defaultValue: 100,
        min: 10,
        max: 1000,
      },
      {
        key: "opacity",
        type: "range",
        nameKey: "actions.image_watermark.params.opacity",
        required: true,
        defaultValue: 0.5,
        min: 0.1,
        max: 1.0,
        step: 0.1,
      },
      {
        key: "customX",
        type: "number",
        nameKey: "actions.image_watermark.params.customX",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
      {
        key: "customY",
        type: "number",
        nameKey: "actions.image_watermark.params.customY",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.watermarkPath || params.watermarkPath.trim() === "") {
        errors.push("actions.image_watermark.errors.no_watermark_file");
      }
      if (params.width < 10 || params.width > 1000) {
        errors.push("actions.image_watermark.errors.invalid_width");
      }
      if (params.opacity < 0.1 || params.opacity > 1.0) {
        errors.push("actions.image_watermark.errors.invalid_opacity");
      }
      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
  {
    id: ActionIds.VIDEO_WATERMARK,
    nameKey: "actions.video_watermark.name",
    descriptionKey: "actions.video_watermark.description",
    categoryId: CategoryIds.WATERMARK,
    inputTypes: ["video"],
    outputTypes: ["video"],
    preview: true,
    order: 30,
    params: [
      {
        key: "watermarkPath",
        type: "file",
        nameKey: "actions.video_watermark.params.watermarkPath",
        required: true,
        defaultValue: "",
      },
      {
        key: "width",
        type: "number",
        nameKey: "actions.video_watermark.params.width",
        required: true,
        defaultValue: 100,
        min: 10,
        max: 1000,
        step: 1,
      },
      {
        key: "opacity",
        type: "range",
        nameKey: "actions.video_watermark.params.opacity",
        required: true,
        defaultValue: 0.5,
        min: 0.1,
        max: 1.0,
        step: 0.1,
      },
      {
        key: "position",
        type: "select",
        nameKey: "actions.video_watermark.params.position",
        required: true,
        defaultValue: "左上",
        options: [
          { value: "左上", labelKey: "positions.top_left" },
          { value: "右上", labelKey: "positions.top_right" },
          { value: "左下", labelKey: "positions.bottom_left" },
          { value: "右下", labelKey: "positions.bottom_right" },
          { value: "自定义", labelKey: "positions.custom" },
        ],
      },
      {
        key: "customX",
        type: "number",
        nameKey: "actions.video_watermark.params.customX",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
      {
        key: "customY",
        type: "number",
        nameKey: "actions.video_watermark.params.customY",
        required: false,
        defaultValue: 10,
        min: 0,
        dependsOn: "position",
        showWhen: "自定义",
      },
    ],
    validate: (params: Record<string, any>) => {
      const errors = [];
      if (!params.watermarkPath || params.watermarkPath.trim() === "") {
        errors.push("actions.video_watermark.errors.no_watermark_file");
      }

      const width = parseInt(params.width);
      if (isNaN(width)) {
        errors.push("actions.video_watermark.errors.invalid_width_number");
      } else if (width < 10 || width > 1000) {
        errors.push("actions.video_watermark.errors.invalid_width");
      }

      const opacity = parseFloat(params.opacity);
      if (isNaN(opacity)) {
        errors.push("actions.video_watermark.errors.invalid_opacity_number");
      } else if (opacity < 0.1 || opacity > 1.0) {
        errors.push("actions.video_watermark.errors.invalid_opacity");
      }

      return {
        isValid: errors.length === 0,
        errors,
      };
    },
  },
];

// 应用映射并导�?
export const watermarkActions: Action[] =
  rawWatermarkActions.map(applyActionMapping);
